<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5">

	<display-name>洪荒Online</display-name>
	<description>洪荒Online游戏系统</description>

	<!-- 会话配置 -->
	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>

	<!-- 错误页面配置 -->
	<error-page>
		<error-code>404</error-code>
		<location>/error/404.jsp</location>
	</error-page>

	<error-page>
		<error-code>500</error-code>
		<location>/error/500.jsp</location>
	</error-page>

	<!-- 项目根目录默认访问页面 -->
	<welcome-file-list>
		<welcome-file>channel.jsp</welcome-file>
	</welcome-file-list>

	<!-- 字符过滤类 编码方式为UTF-8 -->
	<filter>
		<filter-name>EncodingFilter</filter-name>
		<filter-class>com.pub.filter.EncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>EncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- 清除缓存 -->
	<filter>
		<filter-name>ClearCacheFilter</filter-name>
		<filter-class>com.pub.filter.ClearCacheFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>ClearCacheFilter</filter-name>
		<url-pattern>*.do</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>ClearCacheFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
	</filter-mapping>

	<!-- 监听玩家交易的过滤器 -->
	<filter>
		<filter-name>SecurityFilter</filter-name>
		<filter-class>com.pub.SecurityFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>SecurityFilter</filter-name>
		<url-pattern>*.do</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>SecurityFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
	</filter-mapping>

	<!-- Struts配置 -->
	<servlet>
		<servlet-name>action</servlet-name>
		<servlet-class>org.apache.struts.action.ActionServlet</servlet-class>
		<init-param>
			<param-name>config</param-name>
			<param-value>/WEB-INF/struts-config.xml,/WEB-INF/struts-config-ls.xml</param-value>
		</init-param>
		<init-param>
			<param-name>debug</param-name>
			<param-value>2</param-value>
		</init-param>
		<init-param>
			<param-name>detail</param-name>
			<param-value>2</param-value>
		</init-param>
		<load-on-startup>2</load-on-startup>
	</servlet>

	<servlet-mapping>
		<servlet-name>action</servlet-name>
		<url-pattern>*.do</url-pattern>
	</servlet-mapping>

</web-app>

