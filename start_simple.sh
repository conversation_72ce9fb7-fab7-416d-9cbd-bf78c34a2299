#!/bin/bash

echo "🚀 启动洪荒Online H5版本"
echo "========================"

# 设置Tomcat路径
TOMCAT_HOME="/opt/homebrew/opt/tomcat@8/libexec"
WEBAPP_DIR="$TOMCAT_HOME/webapps/jygame"

# 停止Tomcat
echo "🛑 停止Tomcat..."
brew services stop tomcat@8

# 清理旧部署
echo "🧹 清理旧部署..."
rm -rf "$WEBAPP_DIR"
mkdir -p "$WEBAPP_DIR"

# 复制静态文件
echo "📁 复制静态文件..."
cp -r web/* "$WEBAPP_DIR/"

# 创建简化的web.xml
echo "⚙️ 创建web.xml配置..."
cat > "$WEBAPP_DIR/WEB-INF/web.xml" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">
    
    <display-name>洪荒Online H5版本</display-name>
    
    <!-- 默认首页 -->
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
        <welcome-file>pages/login.html</welcome-file>
    </welcome-file-list>
    
    <!-- 字符编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>org.apache.catalina.filters.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>ignore</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>
    
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    
</web-app>
EOF

# 创建简单的首页
echo "🏠 创建首页..."
cat > "$WEBAPP_DIR/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒Online - H5版本</title>
    <style>
        body {
            font-family: "宋体", "SimSun", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 500px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: transform 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .features {
            margin-top: 30px;
            text-align: left;
        }
        .feature {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 洪荒Online</h1>
        <div class="subtitle">现代化H5版本 - 前后端分离架构</div>
        
        <div>
            <a href="pages/login.html" class="btn">🚀 开始游戏</a>
            <a href="pages/game.html" class="btn">🎯 游戏大厅</a>
        </div>
        
        <div class="features">
            <div class="feature">
                ✅ <strong>现代化界面</strong> - 响应式设计，支持桌面和移动端
            </div>
            <div class="feature">
                ✅ <strong>前后端分离</strong> - H5 + RESTful API架构
            </div>
            <div class="feature">
                ✅ <strong>中文支持</strong> - 完美的UTF-8编码支持
            </div>
            <div class="feature">
                ✅ <strong>快速响应</strong> - 优化的加载速度和用户体验
            </div>
        </div>
        
        <div style="margin-top: 30px; color: #888; font-size: 0.9em;">
            版本: H5-1.0 | 编码: UTF-8 | 架构: 前后端分离
        </div>
    </div>
</body>
</html>
EOF

# 启动Tomcat
echo "▶️ 启动Tomcat..."
brew services start tomcat@8

# 等待启动
echo "⏳ 等待应用启动..."
sleep 5

# 测试应用
echo "🧪 测试应用..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/jygame/)
echo "主页状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 应用启动成功！"
    echo ""
    echo "📝 访问地址:"
    echo "  🏠 主页: http://localhost:8083/jygame/"
    echo "  🚀 登录页面: http://localhost:8083/jygame/pages/login.html"
    echo "  🎯 游戏大厅: http://localhost:8083/jygame/pages/game.html"
    echo ""
    echo "🎮 现在可以开始体验洪荒Online H5版本了！"
else
    echo "❌ 应用启动失败，状态码: $HTTP_CODE"
    echo "📋 检查Tomcat日志:"
    echo "tail -f $TOMCAT_HOME/logs/catalina.out"
fi
